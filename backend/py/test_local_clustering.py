#!/usr/bin/env python3
"""
Test script for local clustering functionality.

This script tests the local clustering provider to ensure it works correctly
without requiring external clustering services.
"""

import sys
import os
import logging

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_local_clustering():
    """Test the local clustering provider."""
    try:
        from core.providers.clustering import LocalClusteringProvider
        
        # Test data - simple graph with 6 nodes and some relationships
        test_relationships = [
            {"id": "1", "subject": "A", "object": "B", "weight": 1.0},
            {"id": "2", "subject": "B", "object": "C", "weight": 1.0},
            {"id": "3", "subject": "A", "object": "C", "weight": 1.0},
            {"id": "4", "subject": "D", "object": "E", "weight": 1.0},
            {"id": "5", "subject": "E", "object": "F", "weight": 1.0},
            {"id": "6", "subject": "D", "object": "F", "weight": 1.0},
        ]
        
        # Test parameters
        leiden_params = {
            "resolution": 1.0,
            "randomness": 0.001,
            "max_cluster_size": 1000,
            "extra_forced_iterations": 0,
            "use_modularity": True,
            "random_seed": 7272,
            "weight_attribute": "weight"
        }
        
        logger.info("Testing local clustering provider...")
        
        # Check if dependencies are available
        if not LocalClusteringProvider.is_available():
            logger.error("Local clustering dependencies not available")
            return False
        
        # Create provider and test clustering
        provider = LocalClusteringProvider()
        
        logger.info(f"Provider info: {provider.get_clustering_info()}")
        
        # Perform clustering
        result = provider.cluster_relationships(test_relationships, leiden_params)
        
        logger.info(f"Clustering completed successfully!")
        logger.info(f"Number of community assignments: {len(result)}")
        
        # Print results
        for assignment in result:
            logger.info(f"Node: {assignment['node']}, Cluster: {assignment['cluster']}, Level: {assignment['level']}")
        
        # Verify result structure
        if not result:
            logger.warning("No community assignments returned")
            return False
        
        # Check that all nodes are assigned
        assigned_nodes = {assignment['node'] for assignment in result}
        expected_nodes = {"A", "B", "C", "D", "E", "F"}
        
        if assigned_nodes != expected_nodes:
            logger.error(f"Missing nodes in assignments. Expected: {expected_nodes}, Got: {assigned_nodes}")
            return False
        
        logger.info("✅ Local clustering test passed!")
        return True
        
    except ImportError as e:
        logger.error(f"Import error: {e}")
        logger.error("Please install required dependencies: pip install networkx 'graspologic[leiden]'")
        return False
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_behavior():
    """Test the fallback behavior in the graphs handler."""
    try:
        # Temporarily unset CLUSTERING_SERVICE_URL to test fallback
        original_url = os.environ.get("CLUSTERING_SERVICE_URL")
        if "CLUSTERING_SERVICE_URL" in os.environ:
            del os.environ["CLUSTERING_SERVICE_URL"]
        
        logger.info("Testing fallback behavior (no CLUSTERING_SERVICE_URL set)...")
        
        # This would normally be tested with the actual graphs handler,
        # but for now we just verify the environment variable behavior
        clustering_url = os.environ.get("CLUSTERING_SERVICE_URL")
        if clustering_url is None:
            logger.info("✅ CLUSTERING_SERVICE_URL correctly unset for fallback test")
        else:
            logger.error("❌ CLUSTERING_SERVICE_URL should be unset for this test")
            return False
        
        # Restore original environment
        if original_url:
            os.environ["CLUSTERING_SERVICE_URL"] = original_url
        
        return True
        
    except Exception as e:
        logger.error(f"Fallback test failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting local clustering tests...")
    
    success = True
    
    # Test 1: Local clustering provider
    if not test_local_clustering():
        success = False
    
    # Test 2: Fallback behavior
    if not test_fallback_behavior():
        success = False
    
    if success:
        logger.info("🎉 All tests passed!")
        sys.exit(0)
    else:
        logger.error("❌ Some tests failed!")
        sys.exit(1)
