#!/usr/bin/env python3
"""Check clustering dependencies."""

def check_dependencies():
    print("Checking clustering dependencies...")
    
    # Check networkx
    try:
        import networkx as nx
        print(f"✅ networkx: {nx.__version__}")
    except ImportError as e:
        print(f"❌ networkx: {e}")
        return False
    
    # Check graspologic
    try:
        import graspologic
        print(f"✅ graspologic: {graspologic.__version__}")
    except ImportError as e:
        print(f"❌ graspologic: {e}")
        return False
    
    # Check hierarchical_leiden
    try:
        from graspologic.partition import hierarchical_leiden
        print("✅ hierarchical_leiden: available")
    except ImportError as e:
        print(f"❌ hierarchical_leiden: {e}")
        return False
    
    return True

if __name__ == "__main__":
    if check_dependencies():
        print("\n🎉 All dependencies are available!")
    else:
        print("\n❌ Some dependencies are missing!")
