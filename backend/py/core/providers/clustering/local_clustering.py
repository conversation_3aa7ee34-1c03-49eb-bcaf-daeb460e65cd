"""
Local clustering provider using networkx and graspologic.

This module provides local graph clustering functionality as a fallback
when external clustering services are not available.
"""

import logging
from typing import Any, Dict, List, Optional

try:
    import networkx as nx
    from graspologic.partition import hierarchical_leiden
    CLUSTERING_AVAILABLE = True
except ImportError:
    CLUSTERING_AVAILABLE = False
    nx = None
    hierarchical_leiden = None

from core.base import R2RException

logger = logging.getLogger(__name__)


class LocalClusteringProvider:
    """
    Local clustering provider using networkx and graspologic libraries.
    
    This provider implements the same clustering logic as the external clustering service
    but runs locally to avoid dependency on external services.
    """
    
    def __init__(self):
        if not CLUSTERING_AVAILABLE:
            raise R2RException(
                "Local clustering dependencies not available. "
                "Please install: pip install networkx 'graspologic[leiden]'",
                status_code=500
            )
        
        logger.info("Local clustering provider initialized")
    
    def cluster_relationships(
        self, 
        relationships: List[Dict[str, Any]], 
        leiden_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Cluster relationships using the Leiden algorithm.
        
        Args:
            relationships: List of relationship dictionaries with keys:
                - id: Unique identifier
                - subject: Subject node
                - object: Object node  
                - weight: Relationship weight (default 1.0)
            leiden_params: Parameters for Leiden clustering:
                - resolution: Resolution parameter (default 1.0)
                - randomness: Randomness parameter (default 0.001)
                - max_cluster_size: Maximum cluster size (default 1000)
                - extra_forced_iterations: Extra iterations (default 0)
                - use_modularity: Use modularity (default True)
                - random_seed: Random seed (default 7272)
                - weight_attribute: Weight attribute name (default "weight")
        
        Returns:
            List of community assignments with keys:
                - node: Node identifier
                - cluster: Cluster identifier
                - level: Hierarchical level
        """
        logger.info(f"Starting local clustering for {len(relationships)} relationships")
        
        try:
            # Build graph from relationships
            G = nx.Graph()
            for rel in relationships:
                subject = rel.get("subject", "")
                obj = rel.get("object", "")
                weight = rel.get("weight", 1.0)
                rel_id = rel.get("id", "")
                
                if subject and obj:  # Only add edges with valid nodes
                    G.add_edge(subject, obj, weight=weight, id=rel_id)
            
            if G.number_of_nodes() == 0:
                logger.warning("No valid nodes found in relationships")
                return []
            
            logger.info(f"Built graph with {G.number_of_nodes()} nodes and {G.number_of_edges()} edges")
            
            # Set default parameters
            clustering_params = {
                "resolution": leiden_params.get("resolution", 1.0),
                "randomness": leiden_params.get("randomness", 0.001),
                "max_cluster_size": leiden_params.get("max_cluster_size", 1000),
                "extra_forced_iterations": leiden_params.get("extra_forced_iterations", 0),
                "use_modularity": leiden_params.get("use_modularity", True),
                "random_seed": leiden_params.get("random_seed", 7272),
                "weight_attribute": leiden_params.get("weight_attribute", "weight"),
            }
            
            # Compute hierarchical leiden clustering
            logger.info("Starting Leiden clustering")
            communities = hierarchical_leiden(G, **clustering_params)
            logger.info("Leiden clustering complete")
            
            # Convert communities to the expected format
            assignments = []
            for community in communities:
                assignments.append({
                    "node": community.node,
                    "cluster": community.cluster,
                    "level": community.level
                })
            
            logger.info(f"Generated {len(assignments)} community assignments")
            return assignments
            
        except Exception as e:
            logger.error(f"Error in local clustering: {e}", exc_info=True)
            raise R2RException(
                f"Local clustering failed: {str(e)}",
                status_code=500
            )
    
    @staticmethod
    def is_available() -> bool:
        """Check if local clustering dependencies are available."""
        return CLUSTERING_AVAILABLE
    
    def get_clustering_info(self) -> Dict[str, Any]:
        """Get information about the clustering provider."""
        return {
            "provider": "local",
            "available": self.is_available(),
            "backend": "networkx + graspologic",
            "algorithms": ["leiden"]
        }
