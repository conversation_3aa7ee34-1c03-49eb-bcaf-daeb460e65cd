version: '3.8'

services:
  # Graph Clustering Service
  graph_clustering:
    build:
      context: ./backend/services/clustering
      dockerfile: Dockerfile.clustering
    container_name: r2r_graph_clustering
    ports:
      - "7276:7276"
    environment:
      - PYTHONUNBUFFERED=1
    networks:
      - r2r-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7276/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database (if not already running)
  postgres:
    image: pgvector/pgvector:pg16
    container_name: r2r_postgres
    environment:
      POSTGRES_USER: ${R2R_POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${R2R_POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${R2R_POSTGRES_DBNAME:-postgres}
    ports:
      - "${R2R_POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - r2r-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${R2R_POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # R2R Main Application
  r2r:
    build:
      context: ./backend/py
      dockerfile: Dockerfile
    container_name: r2r_app
    ports:
      - "${R2R_PORT:-7272}:${R2R_PORT:-7272}"
    environment:
      - R2R_HOST=${R2R_HOST:-0.0.0.0}
      - R2R_PORT=${R2R_PORT:-7272}
      - R2R_CONFIG_PATH=${R2R_CONFIG_PATH:-/app/r2r}
      - R2R_PROJECT_NAME=${R2R_PROJECT_NAME:-r2r_default}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
      - R2R_POSTGRES_USER=${R2R_POSTGRES_USER:-postgres}
      - R2R_POSTGRES_PASSWORD=${R2R_POSTGRES_PASSWORD:-postgres}
      - R2R_POSTGRES_HOST=postgres
      - R2R_POSTGRES_PORT=5432
      - R2R_POSTGRES_DBNAME=${R2R_POSTGRES_DBNAME:-postgres}
      - CLUSTERING_SERVICE_URL=http://graph_clustering:7276
    depends_on:
      postgres:
        condition: service_healthy
      graph_clustering:
        condition: service_healthy
    networks:
      - r2r-network
    restart: unless-stopped
    volumes:
      - ./backend/py/r2r:/app/r2r
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${R2R_PORT:-7272}/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  r2r-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
